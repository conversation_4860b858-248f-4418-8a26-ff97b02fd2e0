<?php
namespace Comave\ConfigurableImageFallback\Plugin\ConfigurableGraphQl\Model\Resolver;

use Magento\ConfigurableGraphQl\Model\Resolver\Products\ConfigurableProduct as Subject;
use Magento\Catalog\Helper\Image as ImageHelper;
use Psr\Log\LoggerInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\Resolver\ResolveInfo;

class ConfigurableProductPlugin
{
    private ImageHelper    $imageHelper;
    private LoggerInterface $logger;

    public function __construct(
        ImageHelper $imageHelper,
        LoggerInterface $logger
    ) {
        $this->imageHelper = $imageHelper;
        $this->logger      = $logger;
    }

    public function beforeResolve(
        Subject $subject,
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args  = null
    ) {
        $this->logger->info("[CIFallback] 🔍 beforeResolve() called (parent SKU: "
            . ($value['sku'] ?? '[none]') . ")");
        // IMPORTANT: must return the exact same signature arguments:
        return [$field, $context, $info, $value, $args];
    }    

    /**
     * @param Subject       $subject
     * @param array         $result   — what Magento is about to return
     * @param Field         $field
     * @param mixed         $context
     * @param ResolveInfo   $info
     * @param array|null    $value    — the parent product data, including ['image']
     * @param array|null    $args
     * @return array
     */
    public function afterResolve(
        Subject $subject,
        array $result,
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ): array {
        // 1) Log that the plugin ran
        $parentSku = $value['sku'] ?? '[no SKU]';
        $this->logger->info("[CIFallback] afterResolve for parent SKU “{$parentSku}”");

        // 2) If no variants or no parent-image, bail out
        if (empty($result['variants']) || empty($value['image']['url'])) {
            $this->logger->debug('[CIFallback] skipping: no variants or parent image');
            return $result;
        }

        // 3) Compare each child’s image to the placeholder
        $placeholder = $this->imageHelper->getDefaultPlaceholderUrl('image');
        $parentImage = $value['image'];

        foreach ($result['variants'] as & $variant) {
            $childUrl = $variant['product']['image']['url'] ?? null;
            if ($childUrl === $placeholder) {
                $childSku = $variant['product']['sku'] ?? '[no child SKU]';
                $this->logger->info("[CIFallback] swapping placeholder for variant “{$childSku}”");
                $variant['product']['image']         = $parentImage;
                $variant['product']['media_gallery'][] = $parentImage;
            }
        }

        return $result;
    }
}
