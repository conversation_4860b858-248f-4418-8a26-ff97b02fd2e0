<?php
namespace Comave\ConfigurableImageFallback\Plugin\Model;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Helper\Image as ImageHelper;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableType;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Psr\Log\LoggerInterface;

class ProductImageFallbackPlugin
{
    private ImageHelper $imageHelper;
    private ConfigurableType $configurableType;
    private ProductRepositoryInterface $productRepository;
    private LoggerInterface $logger;

    public function __construct(
        ImageHelper $imageHelper,
        ConfigurableType $configurableType,
        ProductRepositoryInterface $productRepository,
        LoggerInterface $logger
    ) {
        $this->imageHelper       = $imageHelper;
        $this->configurableType  = $configurableType;
        $this->productRepository = $productRepository;
        $this->logger            = $logger;
    }

    /**
     * If child->getImage() is the placeholder, return the parent’s image instead.
     */
    public function afterGetImage(Product $subject, $result)
    {
        $placeholder = $this->imageHelper->getDefaultPlaceholderUrl('image');
        $this->logger->debug("[CIFallback] getImage() for SKU {$subject->getSku()}: got “{$result}”");
        if ($result === $placeholder) {
            $parentIds = $this->configurableType->getParentIdsByChild($subject->getId());
            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $this->logger->info("[CIFallback] swapping placeholder for child {$subject->getSku()} → parent {$parent->getSku()} image");
                return $parent->getImage();
            }
        }
        return $result;
    }

    /**
     * If all gallery entries are placeholders, borrow the parent’s gallery.
     */
    public function afterGetMediaGalleryEntries(Product $subject, $entries)
    {
        if (empty($entries)) {
            return $entries;
        }

        $placeholder = $this->imageHelper->getDefaultPlaceholderUrl('image');
        $onlyPlaceholder = true;
        foreach ($entries as $entry) {
            if ($entry->getFile() && strpos($entry->getFile(), $placeholder) === false) {
                $onlyPlaceholder = false;
                break;
            }
        }

        if ($onlyPlaceholder) {
            $parentIds = $this->configurableType->getParentIdsByChild($subject->getId());
            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $this->logger->info("[CIFallback] swapping entire gallery for child {$subject->getSku()} → parent {$parent->getSku()} gallery");
                return $parent->getMediaGalleryEntries();
            }
        }

        return $entries;
    }
}
