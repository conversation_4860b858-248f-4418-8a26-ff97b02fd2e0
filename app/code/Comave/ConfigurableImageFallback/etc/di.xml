<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\ConfigurableGraphQl\Model\Resolver\Products\ConfigurableProduct">
        <plugin
            name="comave_configurable_image_fallback"
            type="Comave\ConfigurableImageFallback\Plugin\ConfigurableGraphQl\Model\Resolver\ConfigurableProductPlugin"
            sortOrder="10"/>
    </type>

    <type name="Magento\Catalog\Model\Product">
        <plugin name="comave_product_image_fallback"
                type="Comave\ConfigurableImageFallback\Plugin\Model\ProductImageFallbackPlugin"
                sortOrder="10"/>
    </type>
    
</config>
